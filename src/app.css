@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

/* Base styles */
html {
	scroll-behavior: smooth;
}

body {
	font-family: 'Inter', system-ui, sans-serif;
	line-height: 1.6;
	color: #1f2937;
	background-color: #fafafa;
}

/* Custom scrollbar */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
	background: #cbd5e1;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #94a3b8;
}

/* Focus styles */
.focus-ring {
	@apply focus:ring-primary-500 focus:ring-2 focus:ring-offset-2 focus:outline-none;
}

/* Button variants */
.btn-primary {
	@apply bg-primary-600 hover:bg-primary-700 focus-ring rounded-lg px-6 py-2.5 font-medium text-white transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50;
}

.btn-secondary {
	@apply bg-secondary-600 hover:bg-secondary-700 focus-ring rounded-lg px-6 py-2.5 font-medium text-white transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50;
}

.btn-outline {
	@apply border-primary-600 text-primary-600 hover:bg-primary-600 focus-ring rounded-lg border-2 px-6 py-2.5 font-medium transition-all duration-200 hover:text-white disabled:cursor-not-allowed disabled:opacity-50;
}

.btn-ghost {
	@apply text-primary-600 hover:bg-primary-50 focus-ring rounded-lg px-6 py-2.5 font-medium transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50;
}

/* Card styles */
.card {
	@apply shadow-soft overflow-hidden rounded-xl border border-neutral-200 bg-white;
}

.card-hover {
	@apply card hover:shadow-medium transition-all duration-300 hover:-translate-y-1;
}

/* Form styles */
.form-input {
	@apply focus:border-primary-500 focus:ring-primary-200 w-full rounded-lg border border-neutral-300 px-4 py-3 transition-colors duration-200 focus:ring-2;
}

.form-label {
	@apply mb-2 block text-sm font-medium text-neutral-700;
}

/* Progress bar */
.progress-bar {
	@apply w-full overflow-hidden rounded-full bg-neutral-200;
}

.progress-fill {
	@apply from-primary-500 to-secondary-500 h-full bg-gradient-to-r transition-all duration-500 ease-out;
}

/* Animations */
.animate-fade-in {
	animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
	animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
	animation: slideDown 0.3s ease-out;
}

.animate-slide-left {
	animation: slideLeft 0.3s ease-out;
}

.animate-slide-right {
	animation: slideRight 0.3s ease-out;
}

.animate-bounce-subtle {
	animation: bounceSubtle 2s ease-in-out infinite;
}

.animate-float {
	animation: float 3s ease-in-out infinite;
}

/* Utility classes */
.text-gradient {
	@apply from-primary-600 to-secondary-600 bg-gradient-to-r bg-clip-text text-transparent;
}

.bg-gradient-primary {
	@apply from-primary-600 to-primary-700 bg-gradient-to-r;
}

.bg-gradient-secondary {
	@apply from-secondary-600 to-secondary-700 bg-gradient-to-r;
}

.bg-gradient-hero {
	@apply from-primary-600 via-primary-700 to-secondary-600 bg-gradient-to-br;
}

/* Additional keyframes for new animations */
@keyframes slideDown {
	from {
		opacity: 0;
		transform: translateY(-20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideLeft {
	from {
		opacity: 0;
		transform: translateX(20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideRight {
	from {
		opacity: 0;
		transform: translateX(-20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes bounceSubtle {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-5px);
	}
}

@keyframes float {
	0%,
	100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-10px);
	}
}
