@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

/* Base styles */
html {
	scroll-behavior: smooth;
}

body {
	font-family: 'Inter', system-ui, sans-serif;
	line-height: 1.6;
	color: #1f2937;
	background-color: #fafafa;
}

/* Custom scrollbar */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
	background: #cbd5e1;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #94a3b8;
}

/* Button variants */
.btn-primary {
	@apply rounded-lg bg-blue-600 px-6 py-2.5 font-medium text-white transition-all duration-200 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50;
}

.btn-secondary {
	@apply rounded-lg bg-green-600 px-6 py-2.5 font-medium text-white transition-all duration-200 hover:bg-green-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50;
}

.btn-outline {
	@apply rounded-lg border-2 border-blue-600 px-6 py-2.5 font-medium text-blue-600 transition-all duration-200 hover:bg-blue-600 hover:text-white focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50;
}

.btn-ghost {
	@apply rounded-lg px-6 py-2.5 font-medium text-blue-600 transition-all duration-200 hover:bg-blue-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50;
}

/* Card styles */
.card {
	@apply overflow-hidden rounded-xl border border-gray-200 bg-white shadow-lg;
}

.card-hover {
	@apply overflow-hidden rounded-xl border border-gray-200 bg-white shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl;
}

/* Form styles */
.form-input {
	@apply w-full rounded-lg border border-gray-300 px-4 py-3 transition-colors duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200;
}

.form-label {
	@apply mb-2 block text-sm font-medium text-gray-700;
}

/* Progress bar */
.progress-bar {
	@apply w-full overflow-hidden rounded-full bg-gray-200;
}

.progress-fill {
	@apply h-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-500 ease-out;
}

/* Animations */
.animate-fade-in {
	animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
	animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
	animation: slideDown 0.3s ease-out;
}

.animate-slide-left {
	animation: slideLeft 0.3s ease-out;
}

.animate-slide-right {
	animation: slideRight 0.3s ease-out;
}

.animate-bounce-subtle {
	animation: bounceSubtle 2s ease-in-out infinite;
}

.animate-float {
	animation: float 3s ease-in-out infinite;
}

/* Utility classes */
.text-gradient {
	@apply bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent;
}

.bg-gradient-primary {
	@apply bg-gradient-to-r from-blue-600 to-blue-700;
}

.bg-gradient-secondary {
	@apply bg-gradient-to-r from-green-600 to-green-700;
}

.bg-gradient-hero {
	@apply bg-gradient-to-br from-blue-600 via-blue-700 to-green-600;
}

/* Additional keyframes for new animations */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideDown {
	from {
		opacity: 0;
		transform: translateY(-20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideLeft {
	from {
		opacity: 0;
		transform: translateX(20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideRight {
	from {
		opacity: 0;
		transform: translateX(-20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes bounceSubtle {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-5px);
	}
}

@keyframes float {
	0%,
	100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-10px);
	}
}
